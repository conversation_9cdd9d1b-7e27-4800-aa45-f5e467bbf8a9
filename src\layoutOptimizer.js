// 布局优化模块 - 减少帖子间距和内边距，保持原始字体大小

// 检查用户设置并自动应用紧凑布局
export function initCompactLayout() {
    const setting = localStorage.getItem('hupu-cleaner-compact-layout');
    // 默认启用紧凑布局，只有明确设置为 'disabled' 时才不启用
    if (setting !== 'disabled') {
        applyCompactLayout();
        // 如果是第一次使用，设置默认值
        if (!setting) {
            localStorage.setItem('hupu-cleaner-compact-layout', 'enabled');
        }
    }
}

// 应用紧凑布局样式
export function applyCompactLayout() {
    // 检查是否已经注入了紧凑布局样式
    if (document.getElementById('hupu-compact-layout-style')) {
        return; // 已经注入过，直接返回
    }

    // 创建样式元素
    const style = document.createElement('style');
    style.id = 'hupu-compact-layout-style';
    style.textContent = `
        /* 紧凑帖子列表样式 - 保持字体大小，只减少间距 */
        .bbs-sl-web-post-body {
            margin-bottom: 2px !important; /* 进一步减少帖子间距 */
            padding-top: 2px !important; /* 只减少上下内边距 */
            padding-bottom: 2px !important; /* 只减少上下内边距 */
            /* 保持原始的左右padding不变 */
            line-height: 1.1 !important; /* 更紧凑的行高 */
        }

        .bbs-sl-web-post-layout {
            padding: 1px 0 !important; /* 进一步减少布局内边距 */
            line-height: 1.0 !important; /* 最紧凑的行高 */
        }

        /* 调整帖子标题行距 - 保持原字体大小 */
        .post-title {
            margin-bottom: 1px !important; /* 进一步减少标题下边距 */
            margin-top: 0 !important; /* 移除标题上边距 */
            padding: 2px 0 !important; /* 大幅减少标题内边距 */
            line-height: 1.1 !important; /* 更紧凑的标题行高 */
        }

        .post-title .p-title {
            /* 移除字体大小设置，保持原始大小 */
            line-height: 1.1 !important; /* 更紧凑的行高 */
            margin: 0 !important; /* 移除边距 */
            padding: 0 !important; /* 移除内边距 */
        }

        /* 调整帖子数据区域 - 保持原字体大小 */
        .post-datum {
            /* 移除字体大小设置，保持原始大小 */
            margin: 0 !important; /* 进一步减少上下边距 */
            line-height: 1.0 !important; /* 最紧凑的行高 */
        }

        /* 调整作者和时间区域 - 保持原字体大小 */
        .post-auth, .post-time {
            /* 移除字体大小设置，保持原始大小 */
            line-height: 1.0 !important; /* 最紧凑的行高 */
            margin: 0 !important; /* 移除边距 */
        }

        /* 调整页面图标 - 保持原字体大小 */
        .page-icon {
            /* 移除字体大小设置，保持原始大小 */
            margin-left: 1px !important; /* 进一步减少左边距 */
            line-height: 1.0 !important; /* 最紧凑的行高 */
        }

        .page-icon-item {
            /* 移除字体大小设置，保持原始大小 */
            margin: 0 !important; /* 移除边距 */
        }

        /* 调整亮点图标 - 保持原尺寸 */
        .light-icon {
            /* 保持原始图标尺寸 */
            margin: 0 !important; /* 移除图标边距 */
        }

        /* 调整视频图标 - 保持原字体大小 */
        .bbs-sl-web-post-shipin {
            /* 移除字体大小设置，保持原始大小 */
            margin: 0 !important; /* 移除边距 */
        }

        /* 调整iconfont图标 - 保持原字体大小 */
        .iconfont {
            /* 移除字体大小设置，保持原始大小 */
            margin: 0 !important; /* 移除边距 */
        }

        /* 如果存在帖子列表容器，也进行优化 */
        ul {
            margin: 0 !important;
            padding: 0 !important;
            line-height: 1.0 !important; /* 最紧凑的列表行高 */
        }

        /* 针对虎扑特定的列表样式优化 */
        .bbs-sl-web-post-body:hover {
            background-color: #f8f9fa !important; /* 保持悬停效果但使用更淡的颜色 */
        }

        /* 确保所有链接也使用紧凑样式 */
        .bbs-sl-web-post-body a {
            line-height: 1.1 !important;
        }

        /* 更具体地针对虎扑帖子列表的各种元素 */
        li.bbs-sl-web-post-body {
            margin: 0 !important;
            padding-top: 2px !important; /* 只减少上下内边距 */
            padding-bottom: 2px !important; /* 只减少上下内边距 */
            /* 保持原始的左右padding不变 */
        }

        /* 针对帖子内容区域 */
        .bbs-sl-web-post-body > div {
            margin: 0 !important;
            padding: 1px 0 !important; /* 减少内容区域内边距 */
        }

        /* 针对所有可能的标题容器 */
        div.post-title, .post-title div {
            margin: 0 !important;
            padding: 6px 0 !important; /* 强制减少所有标题相关元素的内边距 */
        }

        /* 针对 bbs-sl-web-post-layout 内部的所有 div 元素 */
        .bbs-sl-web-post-layout > div {
            margin: 0 !important;
            padding: 6px 0 !important; /* 大幅减少所有内部div的内边距 */
        }

        /* 更具体地针对各个数据区域 */
        .bbs-sl-web-post-layout .post-title,
        .bbs-sl-web-post-layout .post-datum,
        .bbs-sl-web-post-layout .post-auth,
        .bbs-sl-web-post-layout .post-time {
            margin: 0 !important;
            padding: 6px 0 !important; /* 统一减少所有区域的内边距 */
        }
    `;
    
    // 将样式添加到页面头部
    document.head.appendChild(style);
}

// 移除紧凑布局样式（如果需要恢复原始布局）
export function removeCompactLayout() {
    const style = document.getElementById('hupu-compact-layout-style');
    if (style) {
        style.remove();
    }
}

// 切换紧凑布局（开启/关闭）
export function toggleCompactLayout() {
    const style = document.getElementById('hupu-compact-layout-style');
    if (style) {
        removeCompactLayout();
        return false; // 返回当前状态：已关闭
    } else {
        applyCompactLayout();
        return true; // 返回当前状态：已开启
    }
}
