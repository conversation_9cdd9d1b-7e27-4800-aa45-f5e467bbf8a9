// 用户黑名单模态框 - 原生JavaScript实现
import { getBlockedUsers, saveBlockedUsers } from './storage.js';
import { restoreUserPosts, hidePosts } from './userBlocker.js';
import { toggleCompactLayout } from './layoutOptimizer.js';

export class UserBlockModal {
    constructor(hostElement) {
        this.hostElement = hostElement;
        this.isVisible = false;
        this.userListText = '';
        this.saveSuccess = false;
        this.compactLayoutEnabled = false;

        // 创建 Shadow DOM
        this.shadowRoot = this.hostElement.attachShadow({ mode: 'closed' });

        this.createModal();
        this.bindEvents();
    }

    createModal() {
        // 创建样式
        const style = document.createElement('style');
        style.textContent = this.getModalStyles();

        // 创建模态框HTML结构
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = `
            <div id="user-block-modal-overlay" class="user-block-modal-overlay" style="display: none;">
                <div class="user-block-modal-content">
                    <div class="user-block-modal-header">
                        <h3 class="user-block-modal-title">用户黑名单</h3>
                    </div>
                    <div class="user-block-modal-hint">每行一个用户名，保存时自动去重</div>
                    <textarea
                        id="user-list-textarea"
                        class="user-block-modal-textarea"
                        spellcheck="false"
                        placeholder="请输入要屏蔽的用户名...">
                    </textarea>

                    <!-- 紧凑布局开关 -->
                    <div class="compact-layout-toggle">
                        <span class="compact-layout-label">紧凑布局</span>
                        <button id="compact-toggle-btn" class="compact-toggle-button">
                            <span id="compact-toggle-slider" class="compact-toggle-slider"></span>
                        </button>
                    </div>

                    <div class="user-block-modal-buttons">
                        <button id="save-users-btn" class="user-block-modal-btn save-btn">保存</button>
                        <button id="close-modal-btn" class="user-block-modal-btn close-btn">关闭</button>
                    </div>
                </div>
            </div>
        `;

        // 将样式和HTML添加到Shadow DOM
        this.shadowRoot.appendChild(style);
        this.shadowRoot.appendChild(modalContainer);

        // 获取DOM元素引用（现在从Shadow DOM中获取）
        this.overlay = this.shadowRoot.getElementById('user-block-modal-overlay');
        this.textarea = this.shadowRoot.getElementById('user-list-textarea');
        this.saveBtn = this.shadowRoot.getElementById('save-users-btn');
        this.closeBtn = this.shadowRoot.getElementById('close-modal-btn');
        this.compactToggleBtn = this.shadowRoot.getElementById('compact-toggle-btn');
        this.compactToggleSlider = this.shadowRoot.getElementById('compact-toggle-slider');
    }

    getModalStyles() {
        return `
            .user-block-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 99999999;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: rgba(0, 0, 0, 0.3);
            }

            .user-block-modal-content {
                background: white;
                padding: 24px;
                border-radius: 16px;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
                width: 100%;
                max-width: 380px;
                min-height: 420px;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                border: 1px solid #e5e7eb;
            }

            .user-block-modal-header {
                border-bottom: 1px solid #eee;
                padding-bottom: 8px;
                margin-bottom: 16px;
            }

            .user-block-modal-title {
                font-size: 18px;
                font-weight: bold;
                text-align: center;
                color: #374151;
                margin: 0;
            }

            .user-block-modal-hint {
                margin-bottom: 8px;
                font-size: 12px;
                color: #6b7280;
                text-align: center;
            }

            .user-block-modal-textarea {
                flex: 1;
                resize: none;
                border-radius: 6px;
                border: 2px solid #d1d5db;
                padding: 8px;
                font-size: 15px;
                outline: none;
                background: white;
                font-family: monospace;
                margin-bottom: 12px;
                min-height: 160px;
                scrollbar-width: thin;
                scrollbar-color: #999 transparent;
            }

            .user-block-modal-textarea:focus {
                border-color: #9ca3af;
            }

            .compact-layout-toggle {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 16px;
                padding: 8px;
                background-color: #f9fafb;
                border-radius: 6px;
            }

            .compact-layout-label {
                font-size: 14px;
                color: #374151;
            }

            .compact-toggle-button {
                position: relative;
                display: inline-flex;
                height: 20px;
                width: 36px;
                align-items: center;
                border-radius: 9999px;
                border: none;
                cursor: pointer;
                transition: background-color 0.2s;
                background-color: #d1d5db;
            }

            .compact-toggle-button.enabled {
                background-color: #3b82f6;
            }

            .compact-toggle-slider {
                display: inline-block;
                height: 12px;
                width: 12px;
                border-radius: 50%;
                background-color: white;
                transition: transform 0.2s;
                transform: translateX(4px);
            }

            .compact-toggle-button.enabled .compact-toggle-slider {
                transform: translateX(20px);
            }

            .user-block-modal-buttons {
                display: flex;
                justify-content: center;
                gap: 24px;
                margin-top: 8px;
            }

            .user-block-modal-btn {
                width: 96px;
                border-radius: 6px;
                border: 2px solid #d1d5db;
                padding: 4px 0;
                text-align: center;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s;
                background: white;
            }

            .save-btn {
                border-color: #d1d5db;
                background: white;
            }

            .save-btn:hover {
                border-color: #3b82f6;
                background-color: #dbeafe;
            }

            .save-btn.success {
                border-color: #10b981;
                background-color: #d1fae5;
            }

            .save-btn.success:hover {
                border-color: #10b981;
                background-color: #d1fae5;
            }

            .close-btn:hover {
                border-color: #f87171;
                background-color: #fee2e2;
            }
        `;
    }

    bindEvents() {
        // 关闭按钮事件
        this.closeBtn.addEventListener('click', () => this.hideModal());

        // 保存按钮事件
        this.saveBtn.addEventListener('click', () => this.saveUsers());
        
        // 紧凑布局切换事件
        this.compactToggleBtn.addEventListener('click', () => this.toggleCompactLayoutSetting());

        // 移除点击遮罩层关闭模态框的功能
        // this.overlay.addEventListener('click', (e) => {
        //     if (e.target === this.overlay) {
        //         this.hideModal();
        //     }
        // });
    }

    loadBlockedData() {
        const users = getBlockedUsers();
        this.userListText = users.join('\n');
        this.textarea.value = this.userListText;
    }

    showModal() {
        this.isVisible = true;
        this.overlay.style.display = 'flex';
        this.loadBlockedData();
        // 检查当前紧凑布局状态
        this.compactLayoutEnabled = !!document.getElementById('hupu-enhancer-compact-layout-style');
        this.updateCompactToggleUI();
    }

    hideModal() {
        this.isVisible = false;
        this.overlay.style.display = 'none';
        this.hostElement.style.setProperty('pointer-events', 'none', 'important');
        this.hostElement.style.setProperty('display', 'none', 'important');
    }

    saveUsers() {
        const usersArray = this.textarea.value.split(/\n/).map(line => line.trim()).filter(line => line !== '');
        const uniqueUsers = [...new Set(usersArray)];

        // 获取当前已屏蔽的用户列表
        const currentBlockedUsers = getBlockedUsers();

        // 找出被删除的用户（在当前列表中但不在新列表中）
        const removedUsers = currentBlockedUsers.filter(user => !uniqueUsers.includes(user));

        // 保存新的用户列表
        saveBlockedUsers(uniqueUsers);
        this.userListText = uniqueUsers.join('\n');
        this.textarea.value = this.userListText;

        // 如果有用户被删除，恢复他们的内容
        if (removedUsers.length > 0) {
            restoreUserPosts(removedUsers);
        }

        // 重新隐藏当前屏蔽列表中的用户内容
        hidePosts();

        this.saveSuccess = true;
        this.saveBtn.classList.add('success');
        setTimeout(() => {
            this.saveSuccess = false;
            this.saveBtn.classList.remove('success');
        }, 1500);
    }

    toggleCompactLayoutSetting() {
        const newState = toggleCompactLayout();
        this.compactLayoutEnabled = newState;
        this.updateCompactToggleUI();

        // 保存设置到本地存储
        localStorage.setItem('hupu-enhancer-compact-layout', newState ? 'enabled' : 'disabled');
    }

    updateCompactToggleUI() {
        if (this.compactLayoutEnabled) {
            this.compactToggleBtn.classList.add('enabled');
        } else {
            this.compactToggleBtn.classList.remove('enabled');
        }
    }
}
